package com.example.aimusicplayer.utils

import android.app.Activity
import android.content.Context
import android.graphics.PixelFormat
import android.os.Build
import android.util.Log
import android.view.View
import android.view.ViewGroup
import android.view.Window
import android.view.WindowInsetsController
import android.view.WindowManager
import androidx.annotation.RequiresApi
import androidx.core.view.WindowCompat
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.WindowInsetsControllerCompat

/**
 * 渲染优化工具类
 * 专门用于解决OpenGL渲染问题和提高UI性能
 * 特别针对"Unable to match the desired swap behavior"错误
 */
object RenderingOptimizer {
    private const val TAG = "RenderingOptimizer"

    /**
     * 优化Activity的渲染设置
     * 解决OpenGL渲染问题，特别针对Android Automotive环境
     */
    fun optimizeActivityRendering(activity: Activity) {
        try {
            val window = activity.window

            // 检查是否为车载环境
            val isAutomotive = activity.packageManager.hasSystemFeature("android.hardware.type.automotive")

            // 1. 设置硬件加速
            enableHardwareAcceleration(window)

            // 2. 优化窗口格式
            optimizeWindowFormat(window, isAutomotive)

            // 3. 设置渲染模式
            optimizeRenderingMode(window)

            // 4. 优化布局参数
            optimizeLayoutParams(window, isAutomotive)

            // 5. 车载专用优化
            if (isAutomotive) {
                applyAutomotiveOptimizations(window)
            }

            Log.d(TAG, "Activity渲染优化完成: ${activity.javaClass.simpleName} (车载环境: $isAutomotive)")
        } catch (e: Exception) {
            Log.e(TAG, "Activity渲染优化失败", e)
        }
    }

    /**
     * 启用硬件加速
     */
    private fun enableHardwareAcceleration(window: Window) {
        try {
            // 启用硬件加速
            window.setFlags(
                WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED,
                WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED
            )
            Log.d(TAG, "硬件加速已启用")
        } catch (e: Exception) {
            Log.e(TAG, "启用硬件加速失败", e)
        }
    }

    /**
     * 优化窗口格式
     */
    private fun optimizeWindowFormat(window: Window, isAutomotive: Boolean = false) {
        try {
            // 设置像素格式为RGBA_8888，提供最佳的颜色质量
            window.setFormat(PixelFormat.RGBA_8888)

            // 如果支持，使用TRANSLUCENT格式以获得更好的混合效果
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                window.statusBarColor = android.graphics.Color.TRANSPARENT
                window.navigationBarColor = android.graphics.Color.TRANSPARENT

                // 车载环境下设置更严格的窗口属性
                if (isAutomotive) {
                    window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS)
                }
            }

            Log.d(TAG, "窗口格式优化完成 (车载环境: $isAutomotive)")
        } catch (e: Exception) {
            Log.e(TAG, "窗口格式优化失败", e)
        }
    }

    /**
     * 优化渲染模式 - 使用现代WindowInsetsController API
     */
    private fun optimizeRenderingMode(window: Window) {
        try {
            // 获取decorView
            val decorView = window.decorView

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                // Android 11+ 使用现代 WindowInsetsController
                val controller = window.insetsController
                if (controller != null) {
                    // 隐藏状态栏和导航栏
                    controller.hide(WindowInsetsCompat.Type.systemBars())
                    controller.systemBarsBehavior = WindowInsetsController.BEHAVIOR_SHOW_TRANSIENT_BARS_BY_SWIPE
                    Log.d(TAG, "使用Android 11+ WindowInsetsController")
                } else {
                    Log.w(TAG, "WindowInsetsController为null，使用兼容方式")
                    fallbackToCompatRenderingMode(window, decorView)
                }
            } else {
                // 使用 AndroidX 兼容库
                fallbackToCompatRenderingMode(window, decorView)
            }

            Log.d(TAG, "渲染模式优化完成（使用现代API）")
        } catch (e: Exception) {
            Log.e(TAG, "渲染模式优化失败，回退到传统方式", e)
            // 回退到传统方式
            fallbackToLegacyRenderingMode(window)
        }
    }

    /**
     * 使用AndroidX兼容库设置渲染模式
     */
    private fun fallbackToCompatRenderingMode(window: Window, decorView: View) {
        try {
            WindowCompat.setDecorFitsSystemWindows(window, false)
            val controller = WindowInsetsControllerCompat(window, decorView)

            // 隐藏系统栏
            controller.hide(WindowInsetsCompat.Type.systemBars())
            controller.systemBarsBehavior = WindowInsetsControllerCompat.BEHAVIOR_SHOW_TRANSIENT_BARS_BY_SWIPE
            Log.d(TAG, "使用AndroidX兼容库设置渲染模式")
        } catch (e: Exception) {
            Log.e(TAG, "AndroidX兼容库设置失败", e)
            fallbackToLegacyRenderingMode(window)
        }
    }

    /**
     * 回退到传统的渲染模式设置
     */
    @Suppress("DEPRECATION")
    private fun fallbackToLegacyRenderingMode(window: Window) {
        try {
            val decorView = window.decorView
            // 使用传统的系统UI标志
            decorView.systemUiVisibility = (
                View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                or View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
                or View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                or View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
                or View.SYSTEM_UI_FLAG_FULLSCREEN
                or View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
            )
            Log.d(TAG, "使用传统渲染模式设置")
        } catch (e: Exception) {
            Log.e(TAG, "传统渲染模式设置也失败", e)
        }
    }

    /**
     * 优化布局参数
     */
    private fun optimizeLayoutParams(window: Window, isAutomotive: Boolean = false) {
        try {
            val layoutParams = window.attributes

            // 车载环境下的特殊设置
            if (isAutomotive) {
                // 车载环境保持屏幕常亮
                layoutParams.screenBrightness = WindowManager.LayoutParams.BRIGHTNESS_OVERRIDE_NONE

                // 设置窗口类型为应用窗口
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
                    layoutParams.layoutInDisplayCutoutMode =
                        WindowManager.LayoutParams.LAYOUT_IN_DISPLAY_CUTOUT_MODE_SHORT_EDGES
                }
            } else {
                // 普通环境设置亮度为自动
                layoutParams.screenBrightness = WindowManager.LayoutParams.BRIGHTNESS_OVERRIDE_NONE
            }

            // 优化窗口标志
            layoutParams.flags = layoutParams.flags or
                WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON or
                WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED

            window.attributes = layoutParams

            Log.d(TAG, "布局参数优化完成 (车载环境: $isAutomotive)")
        } catch (e: Exception) {
            Log.e(TAG, "布局参数优化失败", e)
        }
    }

    /**
     * 应用Android Automotive专用优化
     */
    private fun applyAutomotiveOptimizations(window: Window) {
        try {
            // 1. 设置车载专用窗口标志
            window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
            window.addFlags(WindowManager.LayoutParams.FLAG_ALLOW_LOCK_WHILE_SCREEN_ON)

            // 2. 优化车载大屏显示
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
                val layoutParams = window.attributes
                layoutParams.layoutInDisplayCutoutMode =
                    WindowManager.LayoutParams.LAYOUT_IN_DISPLAY_CUTOUT_MODE_SHORT_EDGES
                window.attributes = layoutParams
            }

            // 3. 设置车载环境下的色彩模式
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                try {
                    // 使用反射访问COLOR_MODE_WIDE_COLOR_GAMUT，因为它可能在某些API级别不可用
                    val colorModeField = WindowManager.LayoutParams::class.java.getDeclaredField("COLOR_MODE_WIDE_COLOR_GAMUT")
                    val colorModeValue = colorModeField.getInt(null)
                    window.colorMode = colorModeValue
                } catch (e: Exception) {
                    Log.w(TAG, "设置宽色域失败，使用默认色彩模式", e)
                    // 使用默认色彩模式 (0)
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                        window.colorMode = 0 // COLOR_MODE_DEFAULT 的值
                    }
                }
            }

            // 4. 优化车载环境下的触摸响应
            window.addFlags(WindowManager.LayoutParams.FLAG_SPLIT_TOUCH)

            Log.d(TAG, "Android Automotive专用优化完成")
        } catch (e: Exception) {
            Log.e(TAG, "Android Automotive专用优化失败", e)
        }
    }

    /**
     * 优化视图的硬件加速
     */
    fun optimizeViewHardwareAcceleration(view: View) {
        try {
            when {
                // 对于动画视图，启用硬件加速
                isAnimationView(view) -> {
                    view.setLayerType(View.LAYER_TYPE_HARDWARE, null)
                    Log.d(TAG, "动画视图硬件加速已启用: ${view.javaClass.simpleName}")
                }

                // 对于复杂绘制视图，可能需要软件渲染
                isComplexDrawingView(view) -> {
                    view.setLayerType(View.LAYER_TYPE_SOFTWARE, null)
                    Log.d(TAG, "复杂绘制视图软件渲染已启用: ${view.javaClass.simpleName}")
                }

                // 其他视图使用默认设置
                else -> {
                    view.setLayerType(View.LAYER_TYPE_NONE, null)
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "视图硬件加速优化失败", e)
        }
    }

    /**
     * 判断是否为动画视图
     */
    private fun isAnimationView(view: View): Boolean {
        return view.javaClass.simpleName.contains("Album") ||
               view.javaClass.simpleName.contains("Cover") ||
               view.javaClass.simpleName.contains("Rotation")
    }

    /**
     * 判断是否为复杂绘制视图
     */
    private fun isComplexDrawingView(view: View): Boolean {
        return view.javaClass.simpleName.contains("Lyric") ||
               view.javaClass.simpleName.contains("Spectrum") ||
               view.javaClass.simpleName.contains("Visualizer")
    }

    /**
     * 优化ViewGroup的渲染性能
     */
    fun optimizeViewGroupRendering(viewGroup: ViewGroup) {
        try {
            // 禁用子视图的绘制缓存（已过时的功能，使用@Suppress抑制警告）
            @Suppress("DEPRECATION")
            if (Build.VERSION.SDK_INT < Build.VERSION_CODES.P) {
                // 这些方法在API 28以后已被移除，只在旧版本中使用
                try {
                    val method1 = ViewGroup::class.java.getMethod("setChildrenDrawingCacheEnabled", Boolean::class.java)
                    method1.invoke(viewGroup, false)

                    val method2 = ViewGroup::class.java.getMethod("setChildrenDrawnWithCacheEnabled", Boolean::class.java)
                    method2.invoke(viewGroup, false)
                } catch (e: Exception) {
                    Log.w(TAG, "无法设置绘制缓存属性（API兼容性问题）", e)
                }
            }

            // 设置动画缓存（已过时的功能，使用@Suppress抑制警告）
            @Suppress("DEPRECATION")
            viewGroup.isAnimationCacheEnabled = false

            // 递归优化子视图
            for (i in 0 until viewGroup.childCount) {
                val child = viewGroup.getChildAt(i)
                optimizeViewHardwareAcceleration(child)

                if (child is ViewGroup) {
                    optimizeViewGroupRendering(child)
                }
            }

            Log.d(TAG, "ViewGroup渲染优化完成: ${viewGroup.javaClass.simpleName}")
        } catch (e: Exception) {
            Log.e(TAG, "ViewGroup渲染优化失败", e)
        }
    }

    /**
     * 检查设备的硬件加速支持情况
     */
    fun checkHardwareAccelerationSupport(context: Context): Boolean {
        return try {
            val activity = context as? Activity
            val isHardwareAccelerated = activity?.window?.decorView?.isHardwareAccelerated ?: false

            Log.d(TAG, "硬件加速支持状态: $isHardwareAccelerated")
            isHardwareAccelerated
        } catch (e: Exception) {
            Log.e(TAG, "检查硬件加速支持失败", e)
            false
        }
    }

    /**
     * 监控渲染性能
     */
    @RequiresApi(Build.VERSION_CODES.JELLY_BEAN)
    fun monitorRenderingPerformance(view: View) {
        try {
            view.viewTreeObserver.addOnDrawListener {
                // 监控绘制性能
                Log.v(TAG, "视图绘制: ${view.javaClass.simpleName}")
            }
        } catch (e: Exception) {
            Log.e(TAG, "渲染性能监控设置失败", e)
        }
    }
}
